import { authMiddleware } from '@clerk/nextjs'

export default authMiddleware({
  publicRoutes: [
    '/',
    '/auth(.*)',
    '/portal(.*)',
    '/images(.*)',
    '/favicon.ico',
    '/_next(.*)',
    '/api/webhook(.*)',
    '/api/uploadthing',
    '/chatbot(.*)'
  ],
  ignoredRoutes: [
    '/favicon.ico',
    '/_next(.*)',
    '/api/webhook(.*)',
    '/api/uploadthing'
  ],
})

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
}
