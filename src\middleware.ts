import { authMiddleware } from '@clerk/nextjs'

export default authMiddleware({
  publicRoutes: [
    '/',
    '/auth(.*)',
    '/portal(.*)',
    '/images(.*)',
    '/favicon.ico',
    '/_next(.*)',
    '/api/webhook(.*)',
    '/api/uploadthing',
    '/chatbot(.*)'
  ],
  ignoredRoutes: [
    '/favicon.ico',
    '/_next(.*)',
    '/api/webhook(.*)',
    '/api/uploadthing'
  ],
  // Optimize: Add afterAuth callback for better performance
  afterAuth(auth, req) {
    // Skip processing for static files and API routes that don't need auth
    if (req.nextUrl.pathname.startsWith('/_next') ||
        req.nextUrl.pathname.startsWith('/api/webhook') ||
        req.nextUrl.pathname === '/favicon.ico') {
      return
    }

    // For authenticated users going to auth pages, redirect to dashboard
    if (auth.userId && req.nextUrl.pathname.startsWith('/auth')) {
      const dashboardUrl = new URL('/dashboard', req.url)
      return Response.redirect(dashboardUrl)
    }
  },
})

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
}
