import { onLoginUser } from '@/actions/auth'
import SideBar from '@/components/sidebar'
import { ChatProvider } from '@/context/user-chat-context'
import React, { Suspense } from 'react'
import { Skeleton } from '@/components/ui/skeleton'

type Props = {
  children: React.ReactNode
}

const OwnerLayout = async ({ children }: Props) => {
  const authenticated = await onLoginUser()
  if (!authenticated) return null

  return (
    <ChatProvider>
      <div className="flex h-screen w-full overflow-hidden">
        <SideBar domains={authenticated.domain} />
        <div className="flex-1 h-screen flex flex-col overflow-hidden ml-0 md:ml-[60px] lg:ml-[60px]">
          <main className="flex-1 overflow-auto p-4 md:p-6 relative z-10">
            <Suspense fallback={
              <div className="w-full h-full flex items-center justify-center">
                <div className="space-y-4 w-full max-w-4xl">
                  <Skeleton className="h-8 w-48" />
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[1, 2, 3, 4].map((i) => (
                      <Skeleton key={i} className="h-32 w-full" />
                    ))}
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Skeleton className="h-64 w-full" />
                    <Skeleton className="h-64 w-full" />
                  </div>
                </div>
              </div>
            }>
              {children}
            </Suspense>
          </main>
        </div>
      </div>
    </ChatProvider>
  )
}

export default OwnerLayout
