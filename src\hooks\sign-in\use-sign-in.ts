import { useToast } from '@/components/ui/use-toast'
import { UserLoginProps, UserLoginSchema } from '@/schemas/auth.schema'
import { useSignIn } from '@clerk/nextjs'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'

export const useSignInForm = () => {
  const { isLoaded, setActive, signIn } = useSignIn()
  const [loading, setLoading] = useState<boolean>(false)
  const router = useRouter()
  const { toast } = useToast()
  const methods = useForm<UserLoginProps>({
    resolver: zodResolver(UserLoginSchema),
    mode: 'onChange',
  })
  const onHandleSubmit = methods.handleSubmit(
    async (values: UserLoginProps) => {
      if (!isLoaded) return

      try {
        setLoading(true)
        const authenticated = await signIn.create({
          identifier: values.email,
          password: values.password,
        })

        if (authenticated.status === 'complete') {
          await setActive({ session: authenticated.createdSessionId })

          // Optimized: Show success message and redirect immediately
          toast({
            title: 'Success',
            description: 'Welcome back!',
          })

          // Use replace instead of push for faster navigation
          // and prevent back navigation to login page
          router.replace('/dashboard')
          setLoading(false)
        }
      } catch (error: any) {
        setLoading(false)
        if (error.errors && error.errors[0]) {
          if (error.errors[0].code === 'form_password_incorrect') {
            toast({
              title: 'Error',
              description: 'Email/password is incorrect. Please try again.',
            })
          } else {
            toast({
              title: 'Error',
              description: error.errors[0].longMessage || 'An error occurred during sign in.',
            })
          }
        } else {
          toast({
            title: 'Error',
            description: 'An unexpected error occurred. Please try again.',
          })
        }
      }
    }
  )

  return {
    methods,
    onHandleSubmit,
    loading,
  }
}
