import React from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

const DashboardLoading = () => {
  return (
    <div className="dashboard-safe-zone prevent-masking">
      {/* InfoBar Loading */}
      <div className="w-full p-4 border-b">
        <Skeleton className="h-8 w-48" />
      </div>
      
      <div className="overflow-y-auto w-full dashboard-content flex-1 h-0 p-4 sm:p-6">
        {/* Dashboard Cards Loading */}
        <div className="dashboard-grid prevent-masking mb-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4">
              <CardContent className="p-0">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Plan Usage Loading */}
          <div className="space-y-6">
            <div>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
            <Card className="p-6">
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-12" />
                    </div>
                    <Skeleton className="h-2 w-full" />
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Transactions Loading */}
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex gap-3 items-center">
                <Skeleton className="h-6 w-6" />
                <Skeleton className="h-6 w-32" />
              </div>
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <Skeleton className="h-4 w-12" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardLoading
