'use server'

import { client } from '@/lib/prisma'
import { currentUser, redirectToSignIn } from '@clerk/nextjs'
import { onGetAllAccountDomains } from '../settings'

export const onCompleteUserRegistration = async (
  fullname: string,
  clerkId: string,
  type: string
) => {
  try {
    // Check if user already exists
    const existingUser = await client.user.findUnique({
      where: { clerkId },
    })

    if (existingUser) {
      return { status: 200, user: existingUser }
    }

    const registered = await client.user.create({
      data: {
        fullname,
        clerkId,
        type,
        subscription: {
          create: {},
        },
      },
      select: {
        fullname: true,
        id: true,
        type: true,
      },
    })

    if (registered) {
      return { status: 200, user: registered }
    } else {
      return { status: 400, message: 'Failed to create user' }
    }
  } catch (error) {
    console.error('User registration error:', error)
    return { status: 400, message: 'Database error during user creation' }
  }
}

export const onLoginUser = async () => {
  const user = await currentUser()
  if (!user) redirectToSignIn()
  else {
    try {
      // Optimize: Single query with all needed data including domains
      const authenticated = await client.user.findUnique({
        where: {
          clerkId: user.id,
        },
        select: {
          fullname: true,
          id: true,
          type: true,
          domains: {
            select: {
              name: true,
              icon: true,
              id: true,
              customer: {
                select: {
                  chatRoom: {
                    select: {
                      id: true,
                      live: true,
                    },
                  },
                },
              },
            },
          },
        },
      })
      if (authenticated) {
        return {
          status: 200,
          user: {
            fullname: authenticated.fullname,
            id: authenticated.id,
            type: authenticated.type,
          },
          domain: authenticated.domains
        }
      }
    } catch (error) {
      console.error('Login user error:', error)
      return { status: 400 }
    }
  }
}
